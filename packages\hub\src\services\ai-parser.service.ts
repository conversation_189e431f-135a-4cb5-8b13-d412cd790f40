import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import { aiRouter, AIRouterService } from './aiRouter.service';
import { geocodingService } from './geocoding.service';
import { getSupabaseClient } from '../lib/supabase';
import { cacheService } from './cache.service';
import { redis } from '../config/redis';
import { redisConnectionPool } from './redis-connection-pool.service';
import {
  ParsedTrip,
  ParseSession,
  ParsedTripSchema,
  parsedTripToDbFormat,
  parsedActivityToDbFormat,
  ActivityType,
  normalizeActivityType,
} from '@travelviz/shared';
import { AI_CONFIG, validateAIConfig, getModelConfig } from '../config/ai.config';
import { GeminiService } from './gemini.service';

/**
 * Circuit Breaker for AI API calls
 */
class AICircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private readonly failureThreshold = 3;
  private readonly recoveryTimeout = 60000; // 60 seconds
  private readonly halfOpenTimeout = 30000; // 30 seconds

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      const now = Date.now();
      if (now - this.lastFailureTime >= this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
        logger.info('AI Circuit breaker transitioning to HALF_OPEN');
      } else {
        throw new Error('AI service temporarily unavailable. Circuit breaker is OPEN.');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error);
      throw error;
    }
  }

  private onSuccess(): void {
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
      this.failures = 0;
      logger.info('AI Circuit breaker recovered to CLOSED state');
    }
  }

  private onFailure(error: unknown): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    // Check if it's a rate limit error (don't count against circuit breaker)
    if (error instanceof Error && error.message.includes('Rate limit')) {
      logger.warn('AI rate limit hit, not counting against circuit breaker');
      return;
    }

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      logger.error('AI Circuit breaker OPENED due to failures', {
        failures: this.failures,
        recoveryTime: new Date(Date.now() + this.recoveryTimeout).toISOString()
      });
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failures = 0;
    this.state = 'CLOSED';
    this.lastFailureTime = 0;
  }
}

/**
 * Enhanced AI Parser Service with SSE Progress Updates
 * Provides real-time parsing progress through Redis pub/sub
 */
export class AIParserService {
  private aiRouter: AIRouterService;
  private progressChannel = 'parse:progress:';
  private aiCircuitBreaker: AICircuitBreaker;
  private geminiService: GeminiService;

  constructor() {
    this.aiRouter = aiRouter;
    this.aiCircuitBreaker = new AICircuitBreaker();
    this.geminiService = GeminiService.getInstance();
    validateAIConfig();
    
    // Log circuit breaker initial state
    logger.info('AIParserService initialized', {
      circuitBreakerState: this.aiCircuitBreaker.getState(),
      geminiAvailable: this.geminiService.isAvailable()
    });
  }

  /**
   * Validate AI source type
   */
  private validateAISource(source: string): 'chatgpt' | 'claude' | 'gemini' | 'unknown' {
    const validSources = ['chatgpt', 'claude', 'gemini', 'unknown'];
    return validSources.includes(source) 
      ? source as 'chatgpt' | 'claude' | 'gemini' | 'unknown'
      : 'unknown';
  }

  /**
   * Create a parse session and start parsing
   */
  async createParseSession(content: string, source: string, userId: string): Promise<string> {
    // Check for duplicate request
    const existingSessionId = await this.deduplicateRequest(content, userId);
    if (existingSessionId) {
      logger.info('Duplicate request detected, returning existing session', { 
        sessionId: existingSessionId,
        userId 
      });
      return existingSessionId;
    }

    const sessionId = uuidv4();
    
    // Store initial session in database
    const { error } = await getSupabaseClient()
      .from('ai_import_logs')
      .insert({
        id: sessionId,
        user_id: userId,
        ai_platform: source,
        import_status: 'processing',
        raw_conversation: content.substring(0, 5000), // Limit stored content
        created_at: new Date().toISOString(),
      });

    if (error) {
      logger.error('Failed to create parse session', { error });
      throw new Error('Failed to create parse session');
    }

    // Update deduplication cache with actual session ID
    await this.updateDedupeCache(content, userId, sessionId);

    // Start async parsing - don't await to return immediately
    // Use setTimeout to ensure it runs in the background
    setTimeout(() => {
      logger.info('Starting background parse', { sessionId, source });
      this.parseAsync(sessionId, content, source).catch(error => {
        logger.error('Background parsing failed', { sessionId, error });
      });
    }, 100); // Small delay to ensure response is sent first

    // Publish initial progress
    await this.publishProgress(sessionId, 'initializing', 0, AI_CONFIG.progressMessages.initializing);

    return sessionId;
  }

  /**
   * Get parse session status
   */
  async getSession(sessionId: string): Promise<ParseSession | null> {
    const { data, error } = await getSupabaseClient()
      .from('ai_import_logs')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (error || !data) {
      return null;
    }

    // Map database fields to ParseSession type
    return {
      id: data.id,
      status: data.import_status === 'success' ? 'complete' : 
              data.import_status === 'failed' ? 'error' : 
              data.import_status === 'processing' ? 'processing' : 'pending',
      progress: data.import_status === 'success' ? 100 : 50,
      currentStep: data.import_status === 'processing' ? 'parsing' : 'complete',
      result: data.parsed_data as ParsedTrip | undefined,
      error: data.error_message || undefined,
      startedAt: new Date(data.created_at),
      completedAt: data.import_status === 'success' || data.import_status === 'failed' ? new Date() : undefined,
    };
  }

  /**
   * Check for duplicate requests using content-based fingerprinting
   * Returns existing sessionId if found, null otherwise
   */
  private async deduplicateRequest(content: string, userId: string): Promise<string | null> {
    // Create content hash
    const hash = crypto.createHash('sha256')
      .update(content)
      .update(userId)
      .digest('hex');
    
    const dedupeKey = `parse:dedupe:${hash}`;
    
    return redisConnectionPool.execute(async (redis) => {
      // Try to get existing session ID
      const existingSessionId = await redis.get(dedupeKey);
      
      if (existingSessionId && typeof existingSessionId === 'string') {
        // Check if session is still valid
        const session = await this.getSession(existingSessionId);
        if (session && session.status !== 'error') {
          logger.info('Found existing parse session', { 
            sessionId: existingSessionId,
            status: session.status 
          });
          return existingSessionId;
        }
      }
      
      // No valid existing session found
      // Store placeholder to prevent race conditions
      const tempSessionId = `pending_${uuidv4()}`;
      const result = await redis.set(dedupeKey, tempSessionId, {
        nx: true,  // Only set if not exists
        ex: 500    // 500ms TTL for race condition prevention
      });
      
      if (result === null) {
        // Another request set the key, wait and retry
        await new Promise(resolve => setTimeout(resolve, 100));
        const retrySessionId = await redis.get(dedupeKey);
        if (retrySessionId && typeof retrySessionId === 'string' && !retrySessionId.startsWith('pending_')) {
          return retrySessionId;
        }
      }
      
      // We won the race, will create new session
      // Update the key with longer TTL after session is created
      return null;
    });
  }
  
  /**
   * Update deduplication cache with actual session ID
   */
  private async updateDedupeCache(content: string, userId: string, sessionId: string): Promise<void> {
    const hash = crypto.createHash('sha256')
      .update(content)
      .update(userId)
      .digest('hex');
    
    const dedupeKey = `parse:dedupe:${hash}`;
    
    await redisConnectionPool.execute(async (redis) => {
      // Store actual session ID with 5 minute TTL
      await redis.set(dedupeKey, sessionId, {
        ex: 300  // 5 minutes
      });
    });
  }

  /**
   * Parse content asynchronously with progress updates
   */
  private async parseAsync(
    sessionId: string, 
    content: string, 
    source: string
  ): Promise<void> {
    let currentStep = 'initializing';
    
    try {
      logger.info('parseAsync started', { sessionId, contentLength: content.length, source });
      
      // Step 1: Initializing (10%)
      currentStep = 'initializing';
      await this.publishProgress(sessionId, currentStep, 10, AI_CONFIG.progressMessages.initializing);

      // Use configured primary model (Gemini Flash 2.0)
      const primaryModel = getModelConfig(AI_CONFIG.primaryModel);

      logger.info('Starting AI parse', { 
        sessionId, 
        model: primaryModel?.name || AI_CONFIG.primaryModel,
        source,
        hasGeminiKey: !!process.env.GOOGLE_GEMINI_API_KEY,
        hasOpenRouterKey: !!process.env.OPENROUTER_API_KEY
      });

      currentStep = 'extracting';
      await this.publishProgress(sessionId, currentStep, 30, AI_CONFIG.progressMessages.extracting);

      logger.info('Calling AI API', { source, primaryModel: AI_CONFIG.primaryModel, contentLength: content.length });
      const parsedData = await this.aiCircuitBreaker.execute(
        () => this.callAIAPI(content, source, AI_CONFIG.primaryModel)
      );
      
      logger.info('AI API returned', { 
        hasData: !!parsedData,
        metadataSource: parsedData?.metadata?.source,
        activities: parsedData?.activities?.length || 0 
      });

      // Step 3: Enhancing (60%)
      currentStep = 'enhancing';
      await this.publishProgress(sessionId, currentStep, 60, AI_CONFIG.progressMessages.enhancing);

      // Enhance with geocoding
      const enhancedData = await this.enhanceWithGeocoding(parsedData);

      // Step 4: Validating (80%)
      currentStep = 'validating';
      await this.publishProgress(sessionId, currentStep, 80, AI_CONFIG.progressMessages.validating);

      // Validate parsed data
      const validatedData = ParsedTripSchema.parse(enhancedData);

      // Step 5: Finalizing (95%)
      currentStep = 'finalizing';
      await this.publishProgress(sessionId, currentStep, 95, AI_CONFIG.progressMessages.finalizing);

      // Store success result - wrap in try-catch to handle DB errors
      try {
        const { error: updateError } = await getSupabaseClient()
          .from('ai_import_logs')
          .update({
            import_status: 'success',
            parsed_data: validatedData,
          })
          .eq('id', sessionId);
          
        if (updateError) {
          throw new Error(`Failed to update parse session: ${updateError.message}`);
        }
      } catch (dbError) {
        logger.error('Failed to update parse session to success', { sessionId, dbError });
        throw new Error('Failed to save parsing results to database');
      }

      // Complete (100%)
      await this.publishProgress(sessionId, 'complete', 100, 'Your itinerary is ready!');

      logger.info('Parse completed successfully', { sessionId });

    } catch (error) {
      // Log detailed error information
      logger.error('Parse failed - updating status', { 
        sessionId, 
        currentStep,
        error: error instanceof Error ? {
          message: error.message,
          stack: error.stack,
          name: error.name
        } : error 
      });

      // Determine error message
      let errorMessage = 'Unknown error occurred during parsing';
      if (error instanceof Error) {
        errorMessage = error.message;
        
        if (error.message.includes('Rate limit')) {
          errorMessage = 'Rate limit reached. Please try again.';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Processing timed out. Please try again.';
        } else if (error.message.includes('Invalid')) {
          errorMessage = 'Invalid format. Please check your input.';
        }
      }

      // Always update session status on error
      try {
        await this.publishProgress(sessionId, 'error', 0, errorMessage);
        
        const { error: updateError } = await getSupabaseClient()
          .from('ai_import_logs')
          .update({
            import_status: 'failed',
            error_message: errorMessage,
          })
          .eq('id', sessionId);
          
        if (updateError) {
          logger.error('Failed to update parse session error status', { 
            sessionId, 
            updateError,
            originalError: errorMessage 
          });
        }
      } catch (statusUpdateError) {
        logger.error('Critical: Could not update parse session status after error', {
          sessionId,
          statusUpdateError,
          originalError: errorMessage
        });
      }
      
      // Don't re-throw - we've handled the error by updating the session
      // This prevents unhandled promise rejections in the background job
    }
  }

  /**
   * Publish progress update via Redis pub/sub
   */
  private async publishProgress(
    sessionId: string,
    step: string,
    progress: number,
    message: string
  ): Promise<void> {
    try {
      const channel = `${this.progressChannel}${sessionId}`;
      
      const update = {
        sessionId,
        step,
        progress,
        message,
        timestamp: new Date().toISOString(),
      };

      // Publish to Redis channel
      await redis.publish(channel, JSON.stringify(update));
      
      // Also store latest progress in cache for fallback
      await cacheService.set(
        `progress:${sessionId}`,
        update,
        { ttl: 300 } // 5 minutes TTL
      );

      logger.debug('Published progress update', { sessionId, step, progress });
    } catch (error) {
      logger.error('Failed to publish progress', { sessionId, error });
      // Don't throw - progress updates are not critical
    }
  }

  /**
   * Call AI API for parsing with fallback chain
   */
  private async callAIAPI(
    content: string, 
    source: string,
    modelId: string
  ): Promise<ParsedTrip> {
    const prompt = this.buildPrompt(content, source);
    const modelConfig = getModelConfig(modelId);

    // Handle Gemini Flash 2.0 via native Google API
    if (modelId === 'gemini-flash-2.0') {
      if (this.geminiService.isAvailable()) {
        try {
          logger.info('Using Gemini Flash 2.0 via Google API (free)');
          const fullPrompt = `${AI_CONFIG.systemPrompt}\n\n${prompt}`;
          const geminiResult = await this.geminiService.parseWithGemini(content, fullPrompt);
          
          // Convert Gemini result to expected format with metadata
          const parsedTrip: ParsedTrip = {
            title: geminiResult.title || 'Untitled Trip',
            description: geminiResult.description,
            destination: geminiResult.destination || '',
            startDate: geminiResult.startDate || new Date().toISOString().split('T')[0],
            endDate: geminiResult.endDate || new Date().toISOString().split('T')[0],
            activities: (geminiResult.activities || []).map((activity) => ({
              name: activity.title || 'Activity',
              type: normalizeActivityType(activity.type || ActivityType.activity),
              startTime: activity.startTime || new Date().toISOString(),
              endTime: activity.endTime,
              location: activity.location ? {
                address: activity.location,
                lat: 0,
                lng: 0,
                confidence: 0.8
              } : undefined,
              price: activity.price,
              currency: activity.currency || 'USD',
              dayNumber: activity.day || 1,
              confidence: 0.9
            })),
            metadata: {
              source: this.validateAISource(source),
              confidence: 0.9,
              warnings: [],
              parseDate: new Date().toISOString(),
              version: '1.0'
            }
          };
          
          return parsedTrip;
        } catch (error) {
          logger.warn('Gemini API failed, falling back to next model', { error });
          // Continue to fallback logic
          if (AI_CONFIG.fallbackModels.length > 0) {
            return this.callAIAPIWithFallback(content, source, 0);
          }
          throw error;
        }
      } else {
        // Gemini not available, use fallback immediately
        logger.info('Gemini API key not configured, using fallback models');
        if (AI_CONFIG.fallbackModels.length > 0) {
          return this.callAIAPIWithFallback(content, source, 0);
        }
        throw new Error('No AI models available for parsing');
      }
    }

    // Regular OpenRouter API call
    try {
      const response = await axios.post(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          model: modelConfig?.id || modelId,
          messages: [
            {
              role: 'system',
              content: AI_CONFIG.systemPrompt
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.3,
          max_tokens: 4000,
          response_format: { type: 'json_object' }
        },
        {
          headers: {
            'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://travelviz.app',
            'X-Title': 'TravelViz'
          },
          timeout: 30000
        }
      );

      const aiResponse = response.data.choices[0]?.message?.content;
      if (!aiResponse) {
        throw new Error('No response from AI');
      }

      // Log model usage
      logger.info('AI parsing completed', { 
        model: modelConfig?.name || modelId,
        cost: modelConfig?.costPer1kTokens === 0 ? 'FREE' : `$${modelConfig?.costPer1kTokens || 0}/1k tokens`
      });

      // Parse JSON response
      const parsed = JSON.parse(aiResponse);
      
      // Ensure metadata has correct source value
      const validSource = ['chatgpt', 'claude', 'gemini', 'unknown'].includes(source) 
        ? source as 'chatgpt' | 'claude' | 'gemini' | 'unknown'
        : 'unknown';
      
      // Prepare parsed data
      const parsedData: ParsedTrip = {
        ...parsed,
        metadata: {
          source: validSource,
          confidence: 0.9,
          warnings: [],
          parseDate: new Date().toISOString(),
          version: '1.0'
        }
      };
      
      return parsedData;

    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        if (error.code === 'ECONNABORTED') {
          throw new Error('Request timed out. Please try with a shorter conversation.');
        }
      }
      throw error;
    }
  }

  /**
   * Call AI API with fallback models
   */
  private async callAIAPIWithFallback(
    content: string,
    source: string,
    fallbackIndex: number
  ): Promise<ParsedTrip> {
    if (fallbackIndex >= AI_CONFIG.fallbackModels.length) {
      throw new Error('All AI models failed. Please try again later.');
    }

    const fallbackModelKey = AI_CONFIG.fallbackModels[fallbackIndex];
    const fallbackModel = getModelConfig(fallbackModelKey);

    if (!fallbackModel) {
      return this.callAIAPIWithFallback(content, source, fallbackIndex + 1);
    }

    try {
      logger.info(`Trying fallback model: ${fallbackModel.name}`);
      return await this.callAIAPI(content, source, fallbackModelKey);
    } catch (error) {
      logger.warn(`Fallback model ${fallbackModel.name} failed`, { error });
      return this.callAIAPIWithFallback(content, source, fallbackIndex + 1);
    }
  }

  /**
   * Build AI prompt (simplified for token reduction)
   */
  private buildPrompt(content: string, source: string): string {
    // Limit content to 8000 chars to stay within token limits
    const truncatedContent = content.substring(0, 8000);
    return `Extract trip from ${source} chat:\n${truncatedContent}`;
  }

  /**
   * Enhance parsed data with real geocoding
   */
  private async enhanceWithGeocoding(data: ParsedTrip): Promise<ParsedTrip> {
    if (!geocodingService.isAvailable()) {
      logger.warn('Geocoding service not available, using fallback coordinates');
      return this.addFallbackCoordinates(data);
    }

    try {
      // Collect all unique locations to geocode
      const locationsToGeocode = new Set<string>();
      
      // Add destination
      if (data.destination) {
        locationsToGeocode.add(data.destination);
      }

      // Add activity locations
      data.activities.forEach(activity => {
        if (activity.location?.address) {
          locationsToGeocode.add(activity.location.address);
        }
      });

      // Geocode locations in batches to avoid timeouts
      const GEOCODE_BATCH_SIZE = 10;
      const locations = Array.from(locationsToGeocode);
      const geocodeResults = new Map<string, { lat: number; lng: number; formatted: string }>();
      
      // Process in batches
      for (let i = 0; i < locations.length; i += GEOCODE_BATCH_SIZE) {
        const batch = locations.slice(i, i + GEOCODE_BATCH_SIZE);
        const batchResults = await geocodingService.geocodeBatch(batch);
        
        // Merge results
        batchResults.forEach((value, key) => {
          if (value !== null) {
            geocodeResults.set(key, value);
          }
        });
        
        // Log progress
        logger.info('Geocoding progress', {
          batchIndex: Math.floor(i / GEOCODE_BATCH_SIZE) + 1,
          totalBatches: Math.ceil(locations.length / GEOCODE_BATCH_SIZE),
          locationsProcessed: Math.min(i + GEOCODE_BATCH_SIZE, locations.length),
          totalLocations: locations.length
        });
      }

      // Apply geocoding results to activities
      const enhancedActivities = data.activities.map(activity => {
        if (!activity.location?.address) {
          // No address, use destination as fallback
          const destResult = geocodeResults.get(data.destination);
          if (destResult) {
            return {
              ...activity,
              location: {
                address: data.destination,
                lat: destResult.lat + (Math.random() - 0.5) * 0.01, // Small variation
                lng: destResult.lng + (Math.random() - 0.5) * 0.01,
                confidence: 0.7
              }
            };
          }
          return activity;
        }

        // Has address, try to geocode it
        const result = geocodeResults.get(activity.location.address);
        if (result) {
          return {
            ...activity,
            location: {
              address: result.formatted,
              lat: result.lat,
              lng: result.lng,
              confidence: 0.9
            }
          };
        }

        // Geocoding failed, keep original or add destination coords
        const destResult = geocodeResults.get(data.destination);
        if (destResult) {
          return {
            ...activity,
            location: {
              ...activity.location,
              lat: destResult.lat + (Math.random() - 0.5) * 0.02,
              lng: destResult.lng + (Math.random() - 0.5) * 0.02,
              confidence: 0.5
            }
          };
        }

        return activity;
      });

      return {
        ...data,
        activities: enhancedActivities
      };

    } catch (error) {
      logger.error('Geocoding enhancement failed', { error });
      return this.addFallbackCoordinates(data);
    }
  }

  /**
   * Add fallback coordinates when geocoding fails
   */
  private addFallbackCoordinates(data: ParsedTrip): ParsedTrip {
    // Common city coordinates for fallback
    const cityCoords: Record<string, { lat: number; lng: number }> = {
      'paris': { lat: 48.8566, lng: 2.3522 },
      'london': { lat: 51.5074, lng: -0.1278 },
      'tokyo': { lat: 35.6762, lng: 139.6503 },
      'new york': { lat: 40.7128, lng: -74.0060 },
      'rome': { lat: 41.9028, lng: 12.4964 },
      'barcelona': { lat: 41.3851, lng: 2.1734 },
      'amsterdam': { lat: 52.3676, lng: 4.9041 },
      'bangkok': { lat: 13.7563, lng: 100.5018 },
      'singapore': { lat: 1.3521, lng: 103.8198 },
      'dubai': { lat: 25.2048, lng: 55.2708 },
    };

    const destLower = data.destination.toLowerCase();
    let baseCoords = { lat: 0, lng: 0 };

    // Find matching city
    for (const [city, coords] of Object.entries(cityCoords)) {
      if (destLower.includes(city)) {
        baseCoords = coords;
        break;
      }
    }

    const enhancedActivities = data.activities.map(activity => {
      if (!activity.location || !activity.location.lat) {
        return {
          ...activity,
          location: {
            address: activity.location?.address || data.destination,
            lat: baseCoords.lat + (Math.random() - 0.5) * 0.1,
            lng: baseCoords.lng + (Math.random() - 0.5) * 0.1,
            confidence: 0.6
          }
        };
      }
      return activity;
    });

    return {
      ...data,
      activities: enhancedActivities
    };
  }

  /**
   * Create trip from parsed data
   */
  async createTripFromParse(
    sessionId: string, 
    userId: string,
    edits?: Partial<ParsedTrip>
  ): Promise<string> {
    // Get parse session
    const session = await this.getSession(sessionId);
    if (!session || session.status !== 'complete' || !session.result) {
      throw new Error('Parse session not found or incomplete');
    }

    // Apply any edits
    const tripData = edits ? { ...session.result, ...edits } : session.result;

    // Create trip in database
    const tripId = uuidv4();
    const dbTrip = parsedTripToDbFormat(tripData, userId);

    const { error: tripError } = await getSupabaseClient()
      .from('trips')
      .insert({
        id: tripId,
        ...dbTrip,
        visibility: 'private',
        status: 'planning',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    if (tripError) {
      logger.error('Failed to create trip', { tripError });
      throw new Error('Failed to create trip');
    }

    // Create activities
    if (tripData.activities.length > 0) {
      const activities = tripData.activities.map((activity, index) => ({
        id: uuidv4(),
        ...parsedActivityToDbFormat(activity, tripId, index),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      const { error: activitiesError } = await getSupabaseClient()
        .from('activities')
        .insert(activities);

      if (activitiesError) {
        logger.error('Failed to create activities', { activitiesError });
        // Continue anyway - trip is created
      }
    }

    // Update import log with trip ID
    await getSupabaseClient()
      .from('ai_import_logs')
      .update({
        trip_id: tripId,
      })
      .eq('id', sessionId);

    return tripId;
  }

  /**
   * Get AI circuit breaker status
   */
  getCircuitBreakerStatus(): { state: string; isAvailable: boolean } {
    const state = this.aiCircuitBreaker.getState();
    return {
      state,
      isAvailable: state !== 'OPEN'
    };
  }
}

// Export singleton instance with lazy initialization
let _aiParserService: AIParserService | null = null;

export const getAIParserService = (): AIParserService => {
  if (!_aiParserService) {
    logger.info('Creating AIParserService', {
      hasGeminiKey: !!process.env.GOOGLE_GEMINI_API_KEY,
      hasOpenRouterKey: !!process.env.OPENROUTER_API_KEY,
      nodeEnv: process.env.NODE_ENV
    });
    _aiParserService = new AIParserService();
  }
  return _aiParserService;
};

// For backward compatibility, provide a getter that lazily initializes
export const aiParserService = {
  get instance(): AIParserService {
    return getAIParserService();
  }
};

