import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { GeminiService } from '../src/services/gemini.service';
import { getAIParserService } from '../src/services/ai-parser.service';
import axios from 'axios';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

describe('PDF Import Timeout Fix', () => {
  let geminiService: GeminiService;

  beforeEach(() => {
    vi.clearAllMocks();
    process.env.GOOGLE_GEMINI_API_KEY = 'test-key';
    geminiService = GeminiService.getInstance();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Gemini Service Improvements', () => {
    it('should use increased maxOutputTokens (8192)', async () => {
      const mockResponse = {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: JSON.stringify({
                  title: 'Test Trip',
                  destination: 'Paris',
                  activities: [
                    { title: 'Visit Eiffel Tower', type: 'sightseeing', day: 1 }
                  ]
                })
              }]
            }
          }],
          usageMetadata: {
            promptTokenCount: 100,
            candidatesTokenCount: 200,
            totalTokenCount: 300
          }
        }
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      await geminiService.parseWithGemini('test content', 'test prompt');

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          generationConfig: expect.objectContaining({
            maxOutputTokens: 8192 // Verify increased token limit
          })
        }),
        expect.any(Object)
      );
    });

    it('should detect and handle truncated JSON responses', async () => {
      const truncatedResponse = {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: '{"title": "Test Trip", "activities": [{"title": "Visit"' // Truncated
              }]
            }
          }]
        }
      };

      mockedAxios.post.mockResolvedValueOnce(truncatedResponse);

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow(/truncated/i);
    });

    it('should handle incomplete JSON with proper error message', async () => {
      const incompleteResponse = {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: '{"title": "Test Trip", "activities": ['
              }]
            }
          }]
        }
      };

      mockedAxios.post.mockResolvedValueOnce(incompleteResponse);

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow(/truncated.*complex.*itinerary/i);
    });

    it('should successfully parse valid complete JSON', async () => {
      const validResponse = {
        data: {
          candidates: [{
            content: {
              parts: [{
                text: JSON.stringify({
                  title: 'European Adventure',
                  destination: 'Europe',
                  startDate: '2024-06-01',
                  endDate: '2024-06-15',
                  activities: [
                    {
                      title: 'Visit Eiffel Tower',
                      type: 'sightseeing',
                      location: 'Paris, France',
                      day: 1,
                      startTime: '10:00',
                      price: 25,
                      currency: 'EUR'
                    },
                    {
                      title: 'Sagrada Familia Tour',
                      type: 'sightseeing',
                      location: 'Barcelona, Spain',
                      day: 5,
                      startTime: '14:00',
                      price: 30,
                      currency: 'EUR'
                    }
                  ]
                })
              }]
            }
          }],
          usageMetadata: {
            promptTokenCount: 500,
            candidatesTokenCount: 800,
            totalTokenCount: 1300
          }
        }
      };

      mockedAxios.post.mockResolvedValueOnce(validResponse);

      const result = await geminiService.parseWithGemini('complex itinerary content', 'parse this trip');

      expect(result).toEqual({
        title: 'European Adventure',
        destination: 'Europe',
        startDate: '2024-06-01',
        endDate: '2024-06-15',
        activities: [
          {
            title: 'Visit Eiffel Tower',
            type: 'sightseeing',
            location: 'Paris, France',
            day: 1,
            startTime: '10:00',
            price: 25,
            currency: 'EUR'
          },
          {
            title: 'Sagrada Familia Tour',
            type: 'sightseeing',
            location: 'Barcelona, Spain',
            day: 5,
            startTime: '14:00',
            price: 30,
            currency: 'EUR'
          }
        ]
      });
    });
  });

  describe('AI Parser Service Session Management', () => {
    it('should update session status during parsing steps', async () => {
      const aiParserService = getAIParserService();
      
      // Mock the session creation and status updates
      const mockSession = {
        id: 'test-session-id',
        status: 'processing',
        progress: 0,
        currentStep: 'initializing'
      };

      // Test that session status is properly tracked
      expect(aiParserService).toBeDefined();
      expect(typeof aiParserService.createParseSession).toBe('function');
      expect(typeof aiParserService.getSession).toBe('function');
    });
  });

  describe('Error Handling Improvements', () => {
    it('should provide specific error messages for different failure modes', async () => {
      // Test timeout error
      mockedAxios.post.mockRejectedValueOnce({
        code: 'ECONNABORTED',
        message: 'timeout of 60000ms exceeded'
      });

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow();

      // Test rate limit error
      mockedAxios.post.mockRejectedValueOnce({
        isAxiosError: true,
        response: { status: 429 }
      });

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow(/rate limit/i);

      // Test invalid API key error
      mockedAxios.post.mockRejectedValueOnce({
        isAxiosError: true,
        response: { status: 403 }
      });

      await expect(geminiService.parseWithGemini('test content', 'test prompt'))
        .rejects.toThrow(/invalid.*api key/i);
    });
  });
});
