import axios from 'axios';
import { logger } from '../utils/logger';
import { z } from 'zod';
import { ActivityType, ActivityTypeWithAliases, normalizeActivityType } from '@travelviz/shared';

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

interface ParsedActivity {
  title: string;
  description?: string;
  type?: ActivityType;
  startTime?: string;
  endTime?: string;
  location?: string;
  price?: number;
  currency?: string;
  day?: number;
}

interface ParsedTrip {
  title: string;
  description?: string;
  destination?: string;
  startDate?: string;
  endDate?: string;
  activities: ParsedActivity[];
}

export class GeminiService {
  private static instance: GeminiService;
  private apiKey: string | undefined;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
  private model = 'gemini-2.0-flash-exp';
  private requestCount = 0;
  private lastRequestTime = 0;
  private readonly RATE_LIMIT_DELAY = 1000; // 1 second between requests
  private readonly MAX_RETRIES = 3;
  private readonly INITIAL_RETRY_DELAY = 1000;

  private constructor() {
    this.apiKey = process.env.GOOGLE_GEMINI_API_KEY;
    
    if (!this.apiKey) {
      logger.warn('GOOGLE_GEMINI_API_KEY not set - Gemini parsing will not be available');
    } else {
      logger.info('GeminiService initialized with native API');
    }
  }

  static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }

  /**
   * Check if Gemini service is available
   */
  isAvailable(): boolean {
    return !!this.apiKey;
  }

  /**
   * Wait for rate limit if needed
   */
  private async waitForRateLimit(): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.RATE_LIMIT_DELAY) {
      const waitTime = this.RATE_LIMIT_DELAY - timeSinceLastRequest;
      logger.debug(`Rate limiting: waiting ${waitTime}ms before next request`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  /**
   * Retry with exponential backoff
   */
  private async retryWithBackoff<T>(
    fn: () => Promise<T>,
    attempt = 1
  ): Promise<T> {
    try {
      return await fn();
    } catch (error) {
      if (attempt >= this.MAX_RETRIES) {
        throw error;
      }

      const isRetryable = axios.isAxiosError(error) && 
        (error.response?.status === 429 || 
         error.response?.status === 503 ||
         error.response?.status === 500);

      if (!isRetryable) {
        throw error;
      }

      const delay = this.INITIAL_RETRY_DELAY * Math.pow(2, attempt - 1);
      logger.warn(`Gemini API error, retrying in ${delay}ms (attempt ${attempt}/${this.MAX_RETRIES})`, {
        status: axios.isAxiosError(error) ? error.response?.status : 'unknown',
        attempt
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.retryWithBackoff(fn, attempt + 1);
    }
  }

  /**
   * Parse travel text using native Gemini API
   */
  async parseWithGemini(text: string, prompt: string): Promise<ParsedTrip> {
    if (!this.isAvailable()) {
      throw new Error('Gemini API key not configured');
    }

    try {
      // Apply rate limiting
      await this.waitForRateLimit();

      const startTime = Date.now();
      
      // Make request with retry logic
      const response = await this.retryWithBackoff(async () => {
        return await axios.post(
          `${this.baseUrl}/${this.model}:generateContent`,
          {
            contents: [
              {
                parts: [
                  {
                    text: prompt
                  }
                ]
              }
            ],
            generationConfig: {
              temperature: 0.2,
              maxOutputTokens: 2048,
              topP: 0.8,
              topK: 10
            }
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'x-goog-api-key': this.apiKey
            },
            timeout: 60000 // Increased timeout for PDF processing
          }
        );
      });

      const duration = Date.now() - startTime;
      const geminiResponse = response.data as GeminiResponse;
      
      // Extract text from response
      const generatedText = geminiResponse.candidates?.[0]?.content?.parts?.[0]?.text;
      if (!generatedText) {
        throw new Error('No content in Gemini response');
      }

      // Log token usage
      if (geminiResponse.usageMetadata) {
        logger.info('Gemini API usage', {
          promptTokens: geminiResponse.usageMetadata.promptTokenCount,
          responseTokens: geminiResponse.usageMetadata.candidatesTokenCount,
          totalTokens: geminiResponse.usageMetadata.totalTokenCount,
          duration: `${duration}ms`,
          cost: 'FREE'
        });
      }

      // Parse the JSON response
      return this.parseGeminiResponse(generatedText);
    } catch (error) {
      logger.error('Gemini parsing failed', { error });
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('Gemini API rate limit exceeded');
        } else if (error.response?.status === 403) {
          throw new Error('Invalid Gemini API key');
        }
      }
      
      throw error;
    }
  }

  /**
   * Parse Gemini's response into structured data
   */
  private parseGeminiResponse(response: string): ParsedTrip {
    try {
      // Extract JSON from the response (Gemini might include extra text or code blocks)
      let jsonString = response;
      
      // Remove markdown code blocks if present
      if (response.includes('```json')) {
        jsonString = response.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      } else if (response.includes('```')) {
        jsonString = response.replace(/```\s*/g, '').replace(/```\s*/g, '');
      }
      
      // Extract JSON object
      const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in Gemini response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      
      // Handle different response formats
      if (parsed.activities && Array.isArray(parsed.activities)) {
        // Check if activities have nested structure (day groups)
        const firstActivity = parsed.activities[0];
        if (firstActivity && firstActivity.activities && !firstActivity.title) {
          // Flatten nested day structure
          parsed.activities = parsed.activities.flatMap((day: { activities: unknown[]; day?: number; dayNumber?: number }) => 
            (day.activities as Array<Record<string, unknown>>).map((act) => ({
              ...act,
              day: day.day || day.dayNumber || 1
            }))
          );
        }
        
        // Transform field names to match expected format
        parsed.activities = parsed.activities.map((activity: Record<string, unknown>) => ({
          title: activity.title || activity.name,
          type: activity.type,
          startTime: activity.startTime || activity.start_time,
          location: typeof activity.location === 'object' && activity.location !== null
            ? (activity.location as { address: string }).address 
            : activity.location,
          price: activity.price,
          currency: activity.currency,
          day: activity.day || activity.dayNumber
        }));
      }
      
      // Validate with Zod
      const schema = z.object({
        title: z.string().default('Untitled Trip'),
        destination: z.string().nullable().optional(),
        startDate: z.string().nullable().optional(),
        endDate: z.string().nullable().optional(),
        activities: z.array(z.object({
          title: z.string(),
          type: z.string().transform((val) => normalizeActivityType(val as ActivityTypeWithAliases)).default(ActivityType.activity),
          startTime: z.string().nullable().optional(),
          location: z.string().nullable().optional(),
          price: z.number().nullable().optional(),
          currency: z.string().nullable().optional(),
          day: z.number().nullable().optional()
        })).default([])
      });

      const validated = schema.parse(parsed);
      
      return {
        title: validated.title,
        destination: validated.destination || undefined,
        startDate: validated.startDate || undefined,
        endDate: validated.endDate || undefined,
        activities: validated.activities.map(activity => ({
          title: activity.title,
          type: activity.type,
          startTime: activity.startTime || undefined,
          location: activity.location || undefined,
          price: activity.price || undefined,
          currency: activity.currency || undefined,
          day: activity.day || undefined
        }))
      };
    } catch (error) {
      logger.error('Failed to parse Gemini response', { error, response });
      
      let errorMessage = 'Failed to parse Gemini response';
      if (error instanceof z.ZodError) {
        const issues = error.issues.map(i => `${i.path.join('.')}: ${i.message}`).join(', ');
        errorMessage = `Invalid trip data format from Gemini: ${issues}`;
      } else if (error instanceof SyntaxError) {
        errorMessage = 'Gemini returned invalid JSON format. Please try again.';
      } else if (error instanceof Error) {
        errorMessage = `Failed to parse Gemini response: ${error.message}`;
      }
      
      throw new Error(errorMessage);
    }
  }
}